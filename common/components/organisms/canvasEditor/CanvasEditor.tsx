'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import Konva from 'konva';
import {
  Stage, Layer,
} from 'react-konva';
import { motion } from 'framer-motion';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { LayersPanel } from './LayersPanel';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';

// History management interface
interface HistoryState {
  stageData: string;
  timestamp: number;
}

class CanvasHistoryManager {
  private history: HistoryState[] = [];
  private currentIndex: number = -1;
  private maxHistorySize: number = 50;
  private saveTimeout: NodeJS.Timeout | null = null;
  private stage: Konva.Stage | null = null;
  private onHistoryChange?: (canUndo: boolean, canRedo: boolean) => void;

  constructor(stage: Konva.Stage | null, onHistoryChange?: (canUndo: boolean, canRedo: boolean) => void) {
    this.stage = stage;
    this.onHistoryChange = onHistoryChange;
  }

  setStage(stage: Konva.Stage | null) {
    this.stage = stage;
  }

  setOnHistoryChange(callback: (canUndo: boolean, canRedo: boolean) => void) {
    this.onHistoryChange = callback;
  }

  private notifyHistoryChange() {
    if (this.onHistoryChange) {
      this.onHistoryChange(this.canUndo(), this.canRedo());
    }
  }

  saveState(immediate: boolean = false) {
    if (!this.stage) return;

    // Clear any pending save
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    const saveAction = () => {
      if (!this.stage) return;

      try {
        const stageData = this.stage.toJSON();
        const newState: HistoryState = {
          stageData,
          timestamp: Date.now(),
        };

        // Remove any states after current index (when user made changes after undo)
        this.history = this.history.slice(0, this.currentIndex + 1);

        // Add new state
        this.history.push(newState);
        this.currentIndex = this.history.length - 1;

        // Limit history size
        if (this.history.length > this.maxHistorySize) {
          this.history = this.history.slice(-this.maxHistorySize);
          this.currentIndex = this.history.length - 1;
        }

        this.notifyHistoryChange();
      } catch (error) {
        console.error('Error saving canvas state:', error);
      }
    };

    if (immediate) {
      saveAction();
    } else {
      // Debounce saves to avoid too many history entries
      this.saveTimeout = setTimeout(saveAction, 300);
    }
  }

  undo(): boolean {
    if (!this.canUndo() || !this.stage) return false;

    try {
      this.currentIndex--;
      const state = this.history[this.currentIndex];

      if (state) {
        this.restoreState(state.stageData);
        this.notifyHistoryChange();
        return true;
      }
    } catch (error) {
      console.error('Error during undo:', error);
      this.currentIndex++; // Revert index change
    }

    return false;
  }

  redo(): boolean {
    if (!this.canRedo() || !this.stage) return false;

    try {
      this.currentIndex++;
      const state = this.history[this.currentIndex];

      if (state) {
        this.restoreState(state.stageData);
        this.notifyHistoryChange();
        return true;
      }
    } catch (error) {
      console.error('Error during redo:', error);
      this.currentIndex--; // Revert index change
    }

    return false;
  }

  private restoreState(stageData: string) {
    if (!this.stage) return;

    // Parse the stage data
    const parsedData = JSON.parse(stageData);

    // Get the existing React Konva layer (should be the first layer)
    const existingLayers = this.stage.getLayers();
    let targetLayer = existingLayers[0] as Konva.Layer;

    // If no layer exists, create one
    if (!targetLayer) {
      targetLayer = new Konva.Layer();
      this.stage.add(targetLayer);
    }

    // Clear the existing layer content
    targetLayer.destroyChildren();

    // Restore content to the existing layer
    if (parsedData.children && parsedData.children.length > 0) {
      const layerData = parsedData.children[0]; // Use the first layer's data

      if (layerData.children) {
        layerData.children.forEach((nodeData: any) => {
          const node = this.createNodeFromData(nodeData);
          if (node) {
            targetLayer.add(node);
          }
        });
      }
    }

    // Remove any extra layers that might have been created
    const allLayers = this.stage.getLayers();
    for (let i = 1; i < allLayers.length; i++) {
      allLayers[i].destroy();
    }

    this.stage.batchDraw();

    // Re-add event handlers to all nodes
    this.reattachEventHandlers();
  }

  private createNodeFromData(nodeData: any): Konva.Shape | Konva.Group | null {
    try {
      switch (nodeData.className) {
        case 'Image':
          const imageNode = new Konva.Image(nodeData.attrs);
          addCursorHandlers(imageNode);
          return imageNode;
        case 'Text':
          const textNode = new Konva.Text(nodeData.attrs);
          this.addTextEventHandlers(textNode);
          return textNode;
        case 'Rect':
          const rectNode = new Konva.Rect(nodeData.attrs);
          addCursorHandlers(rectNode);
          return rectNode;
        case 'Circle':
          const circleNode = new Konva.Circle(nodeData.attrs);
          addCursorHandlers(circleNode);
          return circleNode;
        case 'Line':
          const lineNode = new Konva.Line(nodeData.attrs);
          addCursorHandlers(lineNode);
          return lineNode;
        case 'Group':
          return new Konva.Group(nodeData.attrs);
        default:
          // Skip transformer and other non-drawable nodes
          if (nodeData.className === 'Transformer') {
            return null;
          }
          // Try to create generic shape
          try {
            const genericNode = Konva.Node.create(nodeData) as Konva.Shape;
            if (genericNode.draggable && genericNode.draggable()) {
              addCursorHandlers(genericNode);
            }
            return genericNode;
          } catch {
            return null;
          }
      }
    } catch (error) {
      console.error('Error creating node from data:', error);
      return null;
    }
  }

  private reattachEventHandlers() {
    if (!this.stage) return;

    const layers = this.stage.getLayers();
    layers.forEach((layer) => {
      layer.find('Text').forEach((node) => {
        const textNode = node as Konva.Text;
        this.addTextEventHandlers(textNode);
      });

      layer.find('Image').forEach((node) => {
        addCursorHandlers(node);
      });

      // Add cursor handlers to all draggable nodes
      layer.getChildren().forEach((node) => {
        if (node.draggable() && node.getClassName() !== 'Transformer') {
          addCursorHandlers(node);
        }
      });
    });
  }

  private addTextEventHandlers(textNode: Konva.Text) {
    addCursorHandlers(textNode);

    // Add double-click handler for text editing
    textNode.on('dblclick', () => {
      // Dispatch custom event for text editing
      window.dispatchEvent(new CustomEvent('canvasTextEdit', {
        detail: { textNode }
      }));
    });

    textNode.on('click', () => {
      const layer = textNode.getLayer();
      if (!layer || !this.stage) return;

      let transformer = layer.findOne('Transformer') as Konva.Transformer;
      if (!transformer) {
        transformer = new Konva.Transformer();
        layer.add(transformer);
      }

      transformer.nodes([textNode]);
      this.stage.batchDraw();
    });
  }

  canUndo(): boolean {
    return this.currentIndex > 0;
  }

  canRedo(): boolean {
    return this.currentIndex < this.history.length - 1;
  }

  clear() {
    this.history = [];
    this.currentIndex = -1;
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
    this.notifyHistoryChange();
  }

  getCurrentIndex(): number {
    return this.currentIndex;
  }

  getHistoryLength(): number {
    return this.history.length;
  }
}

export const addCursorHandlers = (node: Konva.Node) => {
  if (!node.draggable()) {
    return;
  }
  node.on('mouseover', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });

  node.on('mouseout', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'default';
    }
  });

  node.on('dragstart', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'grabbing';
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  });

  node.on('dragend', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });
};

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const stageRef = useRef<Konva.Stage>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const historyManagerRef = useRef<CanvasHistoryManager | null>(null);
  const [konvaStage, setKonvaStage] = useState<Konva.Stage | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isManualZoom, setIsManualZoom] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showLayers, setShowLayers] = useState(false);
  const [stageSize, setStageSize] = useState<{ width: number; height: number }>(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    return {
      width: canvasSize.width,
      height: canvasSize.height,
    };
  });
  const { activeProject } = useProjectContext();
  
  const updateStageSize = useCallback(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;

    setStageSize({
      width: canvasSize.width,
      height: canvasSize.height,
    });
  }, [platform]);

  const handleZoomChange = useCallback((newZoom: number) => {
    setIsManualZoom(true);
    setZoomLevel(newZoom);
  }, []);

  const fitToView = useCallback(() => {
    if (!canvasContainerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const sceneWidth = canvasSize.width;
    const sceneHeight = canvasSize.height;

    const container = canvasContainerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / sceneWidth;
    const scaleY = containerHeight / sceneHeight;
    const newZoom = Math.min(scaleX, scaleY, 1.25);

    setIsManualZoom(false);
    setZoomLevel(newZoom);
  }, [platform]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const handleResize = () => {
      updateStageSize();
    };

    updateStageSize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, updateStageSize]);

  // Initialize history manager callback
  const handleHistoryChange = useCallback((canUndoState: boolean, canRedoState: boolean) => {
    setCanUndo(canUndoState);
    setCanRedo(canRedoState);
  }, []);

  // Initialize canvas and history manager
  useEffect(() => {
    console.log('CanvasEditor useEffect running', { isOpen, isInitialized });
    if (!stageRef.current || !isOpen || isInitialized) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const stage = stageRef.current;
    stage.width(canvasWidth);
    stage.height(canvasHeight);

    // Initialize history manager
    historyManagerRef.current = new CanvasHistoryManager(stage, handleHistoryChange);

    setKonvaStage(stage);
    setIsInitialized(true);

    setTimeout(() => {
      if (canvasContainerRef.current) {
        fitToView();
        const container = canvasContainerRef.current;
        const scrollLeft = (container.scrollWidth - container.clientWidth) / 2;
        const scrollTop = (container.scrollHeight - container.clientHeight) / 2;
        container.scrollTo({
          left: scrollLeft,
          top: scrollTop,
          behavior: 'smooth',
        });
      }
    }, 300);

    // Save initial state
    historyManagerRef.current.saveState(true);

    // Set up event handlers for history tracking
    const handleStateChange = () => {
      if (historyManagerRef.current) {
        historyManagerRef.current.saveState();
      }
    };

    stage.on('dragend', handleStateChange);
    stage.on('transformend', handleStateChange);

    if (initialImage) {
      const imageObj = new Image();
      imageObj.crossOrigin = 'anonymous';
      imageObj.onload = () => {
        let layer = stage.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          stage.add(layer);
        }

        const canvasWidth = stage.width();
        const canvasHeight = stage.height();
        const imgWidth = imageObj.width;
        const imgHeight = imageObj.height;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        const konvaImage = new Konva.Image({
          image: imageObj,
          x: (canvasWidth - imgWidth * scale) / 2,
          y: (canvasHeight - imgHeight * scale) / 2,
          scaleX: scale,
          scaleY: scale,
          draggable: true,
        });

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);
        layer.batchDraw();

        // Save state after adding initial image
        if (historyManagerRef.current) {
          historyManagerRef.current.saveState(true);
        }
      };
      imageObj.src = initialImage;
    }

    return () => {
      stage.off('dragend', handleStateChange);
      stage.off('transformend', handleStateChange);
    };
  }, [isOpen, initialImage, platform, fitToView, isInitialized, handleHistoryChange]);

  // Separate useEffect for window event listeners
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    // Handle text editing events
    const handleTextEdit = () => {
      if (historyManagerRef.current) {
        historyManagerRef.current.saveState();
      }
    };

    // Handle element addition events
    const handleElementAdded = () => {
      console.log('CanvasEditor: handleElementAdded called');
      if (historyManagerRef.current) {
        historyManagerRef.current.saveState();
      }
    };

    console.log('CanvasEditor: Adding window event listeners');
    window.addEventListener('canvasTextEditComplete', handleTextEdit);
    window.addEventListener('canvasElementAdded', handleElementAdded);

    return () => {
      console.log('CanvasEditor: Removing window event listeners');
      window.removeEventListener('canvasTextEditComplete', handleTextEdit);
      window.removeEventListener('canvasElementAdded', handleElementAdded);
    };
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen && isInitialized) {
      setIsInitialized(false);
      setKonvaStage(null);
      setZoomLevel(1);
      setIsManualZoom(false);
      if (historyManagerRef.current) {
        historyManagerRef.current.clear();
        historyManagerRef.current = null;
      }
    }
  }, [isOpen, isInitialized]);

  const deleteSelectedObjects = useCallback(() => {
    if (!konvaStage) {
      return;
    }

    const transformer = konvaStage.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      return;
    }

    const selectedNodes = transformer.nodes();
    if (selectedNodes.length === 0) {
      return;
    }

    selectedNodes.forEach((node) => {
      node.destroy();
    });

    transformer.nodes([]);
    konvaStage.batchDraw();

    // Save state after deletion
    if (historyManagerRef.current) {
      historyManagerRef.current.saveState();
    }
  }, [konvaStage]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const target = e.target as HTMLElement;
        if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA' && !target.isContentEditable) {
          e.preventDefault();
          deleteSelectedObjects();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, deleteSelectedObjects]);


  const undo = useCallback(() => {
    if (historyManagerRef.current) {
      historyManagerRef.current.undo();
    }
  }, []);

  const redo = useCallback(() => {
    if (historyManagerRef.current) {
      historyManagerRef.current.redo();
    }
  }, []);

  const handleSaveDesign = async () => {
    if (!konvaStage) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    try {
      const dataURL = konvaStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    }
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = stageRef.current;
    if (e.target === stage) {
      const transformer = stage.findOne('Transformer') as Konva.Transformer;
      if (transformer) {
        transformer.nodes([]);
        stage.batchDraw();
      }
      return;
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  };
  console.log(historyManagerRef)
  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
      />

      <motion.div
        className="flex flex-1 min-h-0 flex-col md:flex-row"
        initial={{ opacity: 0  }}
        animate={{ opacity: 1 }}
        transition={{ 
          duration: 0.6, 
          delay: 1.2,
        }}
      >
        <motion.div
          className="block"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 0.5, 
            delay: 1.4,
          }}
        >
          <CanvasSidebar
            canvas={konvaStage}
            agentId={agentId}
            planId={planId}
            containerRef={canvasContainerRef}
            zoomLevel={zoomLevel}
            onClose={onClose}
          />
        </motion.div>
        <motion.div
          className="flex-1 bg-neutral-800 flex flex-col"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 0.6, 
            delay: 1.6,
          }}
        >
          <div
            ref={canvasContainerRef}
            className="flex-1 w-full h-full overflow-auto scroll-smooth relative"
          >
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `calc(100% + ${Math.max(200, stageSize.height * zoomLevel * 0.5)}px)`,
                minWidth: `calc(100% + ${Math.max(200, stageSize.width * zoomLevel * 0.5)}px)`,
                padding: '100px',
              }}
            >
              <div
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center',
                  transition: isManualZoom ? 'none' : 'transform 0.2s ease-out',
                }}
              >
                <Stage
                  ref={stageRef}
                  onClick={handleStageClick}
                  width={stageSize.width}
                  height={stageSize.height}
                  className="block rounded-xl overflow-hidden bg-white shadow-2xl"
                >
                  <Layer />
                </Stage>
              </div>
            </div>

            {/* Layers Panel */}
            {showLayers && (
              <LayersPanel
                canvas={konvaStage}
                onClose={() => setShowLayers(false)}
              />
            )}
          </div>
          <motion.div
            className="text-center text-gray-500 text-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.4,
              delay: 2.0,
            }}
          >
            <div className="flex flex-col md:flex-row items-center justify-between gap-2 md:gap-4">
              {/* <p className="text-center md:text-left">Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p> */}
              <div></div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: 1.8,
                }}
              >
                <FloatingToolbar
                  canvas={konvaStage}
                  zoomLevel={zoomLevel}
                  onZoomChange={handleZoomChange}
                  onFitToView={fitToView}
                  onUndo={undo}
                  onRedo={redo}
                  canUndo={canUndo}
                  canRedo={canRedo}
                  showLayers={showLayers}
                  onToggleLayers={() => setShowLayers(!showLayers)}
                />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};
